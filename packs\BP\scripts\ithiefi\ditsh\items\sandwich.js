import { EntityComponentTypes, ItemLockMode, system } from "@minecraft/server";
export class SandwichComponent {
    onConsume(event) {
        try {
            if (!event.source || event.source.typeId !== "minecraft:player") {
                return;
            }
            const player = event.source;
            player.addEffect("regeneration", 600, {
                amplifier: 0,
                showParticles: false
            });
            player.addEffect("speed", 600, {
                amplifier: 0,
                showParticles: false
            });
            system.runTimeout(() => {
                this.lockSandwichInInventory(player);
            }, 1);
            console.log(`Sandwich consumed by player ${player.name} - effects applied and item locked`);
        }
        catch (error) {
            console.warn(`Failed to handle sandwich consumption: ${error}`);
        }
    }
    lockSandwichInInventory(player) {
        try {
            const inventoryComponent = player.getComponent(EntityComponentTypes.Inventory);
            if (!inventoryComponent) {
                console.warn(`Failed to get inventory component for player ${player.name}`);
                return;
            }
            const container = inventoryComponent.container;
            if (!container) {
                console.warn(`Failed to get container for player ${player.name}`);
                return;
            }
            for (let i = 0; i < container.size; i++) {
                const slot = container.getSlot(i);
                const item = slot.getItem();
                if (item && item.typeId === "ditsh:sandwich") {
                    slot.lockMode = ItemLockMode.slot;
                    slot.keepOnDeath = true;
                    console.log(`Locked sandwich in slot ${i} for player ${player.name}`);
                    break;
                }
            }
        }
        catch (error) {
            console.warn(`Failed to lock sandwich in inventory for player ${player.name}: ${error}`);
        }
    }
}
