import { EntityComponentTypes, ItemLockMode, EquipmentSlot, ItemStack, system } from "@minecraft/server";
export class SandwichComponent {
    onConsume = (event) => {
        try {
            console.warn("Sandwich onConsume called, this context:", this);
            if (!event.source || event.source.typeId !== "minecraft:player") {
                return;
            }
            const player = event.source;
            player.addEffect("regeneration", 600, {
                amplifier: 0,
                showParticles: false
            });
            player.addEffect("speed", 600, {
                amplifier: 0,
                showParticles: false
            });
            system.runTimeout(() => {
                console.warn("Timeout callback called, this context:", this);
                this.addAndLockSandwichInMainHand(player);
            }, 1);
            console.warn(`Sandwich consumed by player ${player.name} - effects applied and item locked`);
        }
        catch (error) {
            console.warn(`Failed to handle sandwich consumption: ${error}`);
        }
    };
    addAndLockSandwichInMainHand = (player) => {
        try {
            console.warn(`Starting addAndLockSandwichInMainHand for player ${player.name}`);
            const equippableComponent = player.getComponent(EntityComponentTypes.Equippable);
            if (!equippableComponent) {
                console.warn(`Failed to get equippable component for player ${player.name}`);
                return;
            }
            console.warn(`Got equippable component for player ${player.name}`);
            const sandwichItem = new ItemStack("ditsh:sandwich", 1);
            console.warn(`Created new sandwich item stack`);
            equippableComponent.setEquipment(EquipmentSlot.Mainhand, sandwichItem);
            console.warn(`Set sandwich in main hand for player ${player.name}`);
            const mainHandSlot = equippableComponent.getEquipmentSlot(EquipmentSlot.Mainhand);
            console.warn(`Got main hand slot for player ${player.name}`);
            mainHandSlot.lockMode = ItemLockMode.slot;
            mainHandSlot.keepOnDeath = true;
            console.warn(`Added and locked new sandwich in main hand for player ${player.name}`);
        }
        catch (error) {
            console.warn(`Failed to add and lock sandwich in main hand for player ${player.name}: ${error}`);
        }
    };
}
