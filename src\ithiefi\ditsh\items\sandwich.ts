/**
 * @fileoverview Sandwich Item Handler
 *
 * This module handles the sandwich item functionality in the DitSH add-on.
 * The sandwich is a special food item that can be eaten when hunger is full,
 * provides regeneration and swiftness effects, and locks in the player's inventory.
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 1.0.0
 *
 * @remarks
 * The sandwich uses a custom item component system to handle consumption events
 * and apply effects directly through TypeScript rather than JSON food components.
 * This allows for more complex behavior like inventory locking.
 *
 * @example
 * ```typescript
 * // Register the sandwich component in main initialization
 * world.beforeEvents.worldInitialize.subscribe((event) => {
 *   event.itemComponentRegistry.registerCustomComponent("ditsh:sandwich", new SandwichComponent());
 * });
 * ```
 */

import {
  Player,
  ItemComponentConsumeEvent,
  EntityInventoryComponent,
  EntityComponentTypes,
  ItemLockMode,
  system
} from "@minecraft/server";

/**
 * Custom component for the sandwich item.
 * Handles consumption events and applies effects with inventory locking.
 *
 * @description This component:
 * - Listens for sandwich consumption events
 * - Applies regeneration and swiftness effects for 30 seconds each
 * - Locks the sandwich item in the player's inventory slot
 * - Prevents the item from being moved, dropped, or crafted with
 *
 * @remarks
 * - Uses ItemLockMode.slot for maximum item restriction
 * - Effects are applied directly through TypeScript for precise control
 * - Item locking ensures the sandwich remains in the player's inventory permanently
 * - The component is registered with the identifier "ditsh:sandwich"
 */
export class SandwichComponent {
  /**
   * Called when the sandwich item is consumed by a player.
   * This is the main handler for sandwich functionality.
   *
   * @param event - The ItemComponentConsumeEvent containing consumption details
   *
   * @description This function:
   * - Verifies the consumer is a player
   * - Applies regeneration effect for 30 seconds (600 ticks)
   * - Applies swiftness effect for 30 seconds (600 ticks)
   * - Locks the sandwich item in the player's inventory slot
   * - Ensures the item persists on death
   *
   * @remarks
   * - Effects are applied with amplifier 0 (level 1)
   * - showParticles is set to false to avoid visual clutter
   * - Item locking is applied after a short delay to ensure proper inventory state
   * - Uses system.runTimeout to handle timing properly
   *
   * @example
   * ```typescript
   * // This method is called automatically when a player consumes the sandwich
   * // No manual invocation required
   * ```
   */
  onConsume(event: ItemComponentConsumeEvent): void {
    try {
      // Verify the source is a player
      if (!event.source || event.source.typeId !== "minecraft:player") {
        return;
      }

      const player: Player = event.source as Player;

      // Apply regeneration effect for 30 seconds (600 ticks)
      player.addEffect("regeneration", 600, {
        amplifier: 0, // Level 1 (amplifier 0)
        showParticles: false
      });

      // Apply swiftness effect for 30 seconds (600 ticks)
      player.addEffect("speed", 600, {
        amplifier: 0, // Level 1 (amplifier 0)
        showParticles: false
      });

      // Lock the sandwich item in the player's inventory after a short delay
      // This ensures the inventory state is properly updated after consumption
      system.runTimeout(() => {
        this.lockSandwichInInventory(player);
      }, 1); // 1 tick delay

      console.log(`Sandwich consumed by player ${player.name} - effects applied and item locked`);

    } catch (error) {
      console.warn(`Failed to handle sandwich consumption: ${error}`);
    }
  }

  /**
   * Locks the sandwich item in the player's inventory to prevent removal.
   * This is a private helper method called after sandwich consumption.
   *
   * @param player - The player whose inventory should be searched and locked
   *
   * @description This function:
   * - Gets the player's inventory component
   * - Searches all inventory slots for sandwich items
   * - Applies ItemLockMode.slot to prevent moving/dropping
   * - Sets keepOnDeath to true for persistence
   *
   * @remarks
   * - Only locks the first sandwich found to avoid multiple locks
   * - Uses explicit type casting for proper TypeScript interface support
   * - Handles errors gracefully with console warnings
   * - Searches the entire inventory container, not just hotbar
   *
   * @example
   * ```typescript
   * // Called automatically after sandwich consumption
   * this.lockSandwichInInventory(player);
   * ```
   */
  private lockSandwichInInventory(player: Player): void {
    try {
      // Get the player's inventory component
      const inventoryComponent: EntityInventoryComponent | undefined = player.getComponent(
        EntityComponentTypes.Inventory
      ) as EntityInventoryComponent;

      if (!inventoryComponent) {
        console.warn(`Failed to get inventory component for player ${player.name}`);
        return;
      }

      const container = inventoryComponent.container;
      if (!container) {
        console.warn(`Failed to get container for player ${player.name}`);
        return;
      }

      // Search all inventory slots for the sandwich item
      for (let i = 0; i < container.size; i++) {
        const slot = container.getSlot(i);
        const item = slot.getItem();
        
        if (item && item.typeId === "ditsh:sandwich") {
          // Lock the item in this slot
          slot.lockMode = ItemLockMode.slot;
          slot.keepOnDeath = true;
          
          console.log(`Locked sandwich in slot ${i} for player ${player.name}`);
          break; // Only lock the first sandwich found
        }
      }

    } catch (error) {
      console.warn(`Failed to lock sandwich in inventory for player ${player.name}: ${error}`);
    }
  }
}

/**
 * @fileoverview Sandwich Item Implementation Notes
 *
 * **Key Features:**
 * - Custom item component using onConsume event
 * - Direct effect application through TypeScript
 * - Inventory slot locking with ItemLockMode.slot
 * - Persistence on death with keepOnDeath property
 *
 * **Integration Requirements:**
 * 1. Register the component in world initialization
 * 2. Add component reference to sandwich item JSON
 * 3. Remove old event listener system from index.ts
 * 4. Update main.ts to remove sandwich-specific initialization
 *
 * **Effect Details:**
 * - Regeneration I for 30 seconds (600 ticks)
 * - Speed I for 30 seconds (600 ticks)
 * - No particle effects to avoid visual clutter
 *
 * **Locking Mechanism:**
 * - Uses ItemLockMode.slot for maximum restriction
 * - Prevents moving, dropping, or crafting with the item
 * - Item persists through death and respawn
 * - Only locks the first sandwich found in inventory
 */
