/**
 * @fileoverview Centralized Item Handler System
 *
 * This module provides a centralized system for handling all custom items in the DitSH add-on.
 * It manages item detection, routing to specific handlers, and provides utilities for item management.
 * Also handles registration of custom item components for advanced item functionality.
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 1.0.0
 *
 * @remarks
 * This system follows a registry pattern where each item handler is registered with its item ID.
 * The main handler function checks all players' main hand items and routes to appropriate handlers.
 * Custom item components (like the sandwich) are registered during system startup for advanced behavior.
 * This design allows for easy addition of new items without modifying the core system.
 *
 * @example
 * ```typescript
 * // In main.ts
 * import { handleAllItems, registerSandwichComponent } from "./items/index";
 *
 * // Register custom components
 * system.beforeEvents.startup.subscribe((event) => {
 *   registerSandwichComponent(event.itemComponentRegistry);
 * });
 *
 * // Handle item effects
 * system.runInterval(() => {
 *   handleAllItems();
 * }, 20); // Run every second
 * ```
 */

import {
  world,
  GameMode,
  Player,
  EntityEquippableComponent,
  EntityComponentTypes,
  EquipmentSlot,
  ItemStack,
  ItemComponentRegistry
} from "@minecraft/server";
import { SandwichComponent } from "./sandwich";

// All coil functionality is now centralized in this file
// Individual coil files are no longer needed for the main functionality

/**
 * Type definition for item handler functions
 * Each handler receives the player holding the item
 */
type ItemHandler = (player: Player) => void;

/**
 * Registry mapping item IDs to their corresponding handler functions
 * This allows for easy addition of new items without modifying core logic
 *
 * @example Adding a new item:
 * ```typescript
 * // 1. Create your item handler function in a separate file
 * // 2. Import it at the top of this file
 * // 3. Add it to this registry:
 * ["ditsh:your_new_item", handleYourNewItem]
 * ```
 */
const ITEM_HANDLERS: Map<string, ItemHandler> = new Map([
  ["ditsh:insane_decay_coil", (player: Player) => handleCoilForPlayer(player, "ditsh:insane_decay_coil")],
  ["ditsh:speed_coil", (player: Player) => handleCoilForPlayer(player, "ditsh:speed_coil")],
  ["ditsh:special_coil", (player: Player) => handleCoilForPlayer(player, "ditsh:special_coil")],
]);

/**
 * Configuration for each coil type including sound settings and effects
 */
interface CoilConfig {
  soundId: string;
  soundInterval: number;
  effectId: string;
  effectAmplifier: number;
  effectDuration: number;
}

/**
 * Registry of coil configurations
 */
const COIL_CONFIGS: Map<string, CoilConfig> = new Map([
  ["ditsh:insane_decay_coil", {
    soundId: "item.ditsh.insane_decay_coil.use",
    soundInterval: 5,
    effectId: "speed",
    effectAmplifier: 3, // Swiftness IV
    effectDuration: 40
  }],
  ["ditsh:special_coil", {
    soundId: "item.ditsh.special_coil.use",
    soundInterval: 2,
    effectId: "speed",
    effectAmplifier: 1, // Swiftness II
    effectDuration: 40
  }],
  ["ditsh:speed_coil", {
    soundId: "item.ditsh.speed_coil.use",
    soundInterval: 3,
    effectId: "speed",
    effectAmplifier: 0, // Swiftness I
    effectDuration: 40
  }]
]);

/**
 * Set to track players currently holding any coil item
 * Used for cleanup when players stop holding items
 */
const playersHoldingCoils: Set<string> = new Set();

/**
 * Gets the item currently held in a player's main hand.
 * Uses the EntityEquippableComponent for reliable item detection.
 *
 * @param player - The player to check
 * @returns The ItemStack in the main hand, or undefined if no item or component not available
 *
 * @remarks
 * - Uses explicit type casting for proper TypeScript interface support
 * - Returns undefined if the player doesn't have an equippable component
 * - Returns undefined if no item is in the main hand slot
 *
 * @example
 * ```typescript
 * const mainHandItem = getPlayerMainHandItem(player);
 * if (mainHandItem) {
 *   console.log(`Player is holding: ${mainHandItem.typeId}`);
 * }
 * ```
 */
export function getPlayerMainHandItem(player: Player): ItemStack | undefined {
  try {
    // Get the equippable component from the player
    const equippableComponent: EntityEquippableComponent | undefined = player.getComponent(
      EntityComponentTypes.Equippable
    ) as EntityEquippableComponent;

    if (!equippableComponent) {
      return undefined;
    }

    // Get the item in the main hand
    return equippableComponent.getEquipment(EquipmentSlot.Mainhand);
  } catch (error) {
    console.warn(`Failed to get main hand item for player ${player.name}: ${error}`);
    return undefined;
  }
}

/**
 * Centralized coil sound effect controller.
 * Handles sound timing and playback for all coil types.
 *
 * @param player - The player holding the coil
 * @param itemId - The coil item identifier
 *
 * @description This function:
 * - Gets the coil configuration for the specific item
 * - Manages sound timing using dynamic properties
 * - Plays sound at the configured interval for each coil type
 * - Provides centralized sound management for all coils
 *
 * @remarks
 * - Uses dynamic properties to track sound timing per player per item
 * - Each coil type has its own timer property to avoid conflicts
 * - Sound intervals are configurable per coil type
 * - Automatically resets timer when sound is played
 *
 * @example
 * ```typescript
 * // Called from individual coil handlers
 * handleCoilSoundEffect(player, "ditsh:speed_coil");
 * ```
 */
function handleCoilSoundEffect(player: Player, itemId: string): void {
  try {
    const config = COIL_CONFIGS.get(itemId);
    if (!config) {
      return; // No configuration found for this item
    }

    // Create unique timer property for this coil type
    const timerProperty = `ditsh:${itemId.split(':')[1]}_sound_timer`;

    // Get current timer value
    const soundTimer: number = (player.getDynamicProperty(timerProperty) as number) ?? 0;

    // Check if the configured interval has passed
    if (soundTimer >= config.soundInterval) {
      // Play the sound effect for this specific player only
      player.runCommand(`playsound ${config.soundId} @s ~ ~ ~ 1.0 1.0`);

      // Reset the timer
      player.setDynamicProperty(timerProperty, 0);
    } else {
      // Increment the timer by 1 second (since this runs every second)
      player.setDynamicProperty(timerProperty, soundTimer + 1);
    }

  } catch (error) {
    console.warn(`Failed to handle coil sound effect for player ${player.name}: ${error}`);
  }
}

/**
 * Centralized coil effect controller.
 * Handles effect application for all coil types.
 *
 * @param player - The player holding the coil
 * @param itemId - The coil item identifier
 *
 * @description This function:
 * - Gets the coil configuration for the specific item
 * - Applies the configured effect with proper amplifier and duration
 * - Provides centralized effect management for all coils
 *
 * @remarks
 * - Effect settings are configurable per coil type
 * - Uses showParticles: false to avoid visual clutter
 * - Effect duration ensures no gaps between applications
 *
 * @example
 * ```typescript
 * // Called from individual coil handlers
 * handleCoilEffect(player, "ditsh:special_coil");
 * ```
 */
function handleCoilEffect(player: Player, itemId: string): void {
  try {
    const config = COIL_CONFIGS.get(itemId);
    if (!config) {
      return; // No configuration found for this item
    }

    // Apply the configured effect
    player.addEffect(config.effectId, config.effectDuration, {
      amplifier: config.effectAmplifier,
      showParticles: false // Don't show particles to avoid visual clutter
    });

  } catch (error) {
    console.warn(`Failed to handle coil effect for player ${player.name}: ${error}`);
  }
}

/**
 * Unified coil handler that manages both effects and sounds.
 * This is the main handler function that should be called for all coil types.
 *
 * @param player - The player holding the coil
 * @param itemId - The coil item identifier
 *
 * @description This function:
 * - Tracks that the player is currently holding a coil
 * - Applies the appropriate effect for the coil type
 * - Manages sound timing and playback
 * - Provides unified handling for all coil functionality
 *
 * @remarks
 * - This replaces the individual coil handler functions
 * - Centralizes all coil logic in one place
 * - Maintains the same functionality as individual handlers
 * - Easier to maintain and extend for new coil types
 *
 * @example
 * ```typescript
 * // Used by the centralized item system
 * const handler = ITEM_HANDLERS.get("ditsh:insane_decay_coil");
 * if (handler) {
 *   handler(player);
 * }
 * ```
 */
export function handleCoilForPlayer(player: Player, itemId: string): void {
  try {
    // Track that this player is currently holding a coil
    playersHoldingCoils.add(player.id);

    // Handle effect application
    handleCoilEffect(player, itemId);

    // Handle sound timing and playback
    handleCoilSoundEffect(player, itemId);

  } catch (error) {
    console.warn(`Failed to handle coil for player ${player.name}: ${error}`);
  }
}

/**
 * Registers the sandwich custom component with the item component registry.
 * This function should be called during system startup.
 *
 * @param itemComponentRegistry - The item component registry from startup event
 *
 * @description This function:
 * - Registers the SandwichComponent with identifier "ditsh:sandwich"
 * - Enables custom consumption behavior for the sandwich item
 * - Replaces the old event listener system with component-based handling
 *
 * @remarks
 * - Must be called during system startup event
 * - The component handles all sandwich functionality internally
 * - No longer requires separate event listeners or handlers
 *
 * @example
 * ```typescript
 * // Called from system startup
 * system.beforeEvents.startup.subscribe((event) => {
 *   registerSandwichComponent(event.itemComponentRegistry);
 * });
 * ```
 */
export function registerSandwichComponent(itemComponentRegistry: ItemComponentRegistry): void {
  try {
    itemComponentRegistry.registerCustomComponent("ditsh:sandwich", new SandwichComponent());
    console.log("Sandwich custom component registered successfully");
  } catch (error) {
    console.warn(`Failed to register sandwich component: ${error}`);
  }
}



/**
 * Handles all custom items for all players in the world.
 * This is the main entry point for the item system that should be called from main.ts.
 *
 * @description This function:
 * - Gets all players in survival/adventure mode (excludes creative/spectator)
 * - Checks each player's main hand item using getPlayerMainHandItem
 * - Routes to appropriate item handlers based on item ID
 * - Calls the legacy handleInsaneDecayCoil for backward compatibility
 *
 * @remarks
 * - Currently maintains backward compatibility with the existing insane decay coil system
 * - Future items can be added to the ITEM_HANDLERS registry
 * - Individual item handlers manage their own state and effects
 * - Players in creative/spectator mode are excluded to prevent unintended effects
 *
 * @example
 * ```typescript
 * // Called from main.ts interval system
 * system.runInterval(() => {
 *   try {
 *     handleAllItems();
 *   } catch (error) {
 *     console.warn(`Failed to handle items: ${error}`);
 *   }
 * }, 20);
 * ```
 */
export function handleAllItems(): void {
  try {
    // Get all players excluding creative and spectator modes
    const allPlayers: Player[] = world
      .getAllPlayers()
      .filter((player: Player) =>
        player.getGameMode() !== GameMode.Creative &&
        player.getGameMode() !== GameMode.Spectator
      );

    // Track which players are holding items for cleanup purposes
    const playersWithItems: Set<string> = new Set();

    for (const player of allPlayers) {
      try {
        const mainHandItem = getPlayerMainHandItem(player);

        if (mainHandItem) {
          // Check if we have a handler for this item
          const handler = ITEM_HANDLERS.get(mainHandItem.typeId);
          if (handler) {
            // Call the specific item handler
            handler(player);
            playersWithItems.add(player.id);
          }
        }
      } catch (playerError) {
        console.warn(`Failed to process items for player ${player.name}: ${playerError}`);
      }
    }

    // Clean up tracking for players who are no longer holding items
    // This is handled by individual item cleanup functions called from cleanupAllItemTrackers

  } catch (error) {
    console.warn(`Failed to handle all items: ${error}`);
  }
}

/**
 * Cleans up all item-related tracking data.
 * This function should be called periodically to prevent memory leaks.
 *
 * @description This function:
 * - Calls cleanup functions for all registered item handlers
 * - Removes stale tracking data for disconnected players
 * - Maintains optimal performance by cleaning up unused data
 *
 * @remarks
 * - Should be called periodically (e.g., every 10 seconds) from a cleanup interval
 * - Individual item handlers should export their own cleanup functions
 * - Safe to call frequently as it only processes tracking data
 *
 * @example
 * ```typescript
 * // Called from a cleanup interval in entities/index.ts
 * system.runInterval(() => {
 *   cleanupAllItemTrackers();
 * }, 200); // Every 10 seconds
 * ```
 */
export function cleanupAllItemTrackers(): void {
  try {
    // Get all current players
    const allPlayers: Player[] = world.getAllPlayers();

    // Clean up players who are no longer holding any coils
    // This is done by checking against the current holders set
    const currentHolders: Set<string> = new Set(playersHoldingCoils);
    playersHoldingCoils.clear(); // Clear the set, it will be repopulated by active handlers

    // Reset sound timers for players who stopped holding coils
    for (const player of allPlayers) {
      const playerId: string = player.id;

      // If player was not holding any coil this cycle, reset all their coil timers
      if (!currentHolders.has(playerId)) {
        // Reset timers for all coil types
        for (const [itemId] of COIL_CONFIGS) {
          const timerProperty = `ditsh:${itemId.split(':')[1]}_sound_timer`;
          const currentTimer = player.getDynamicProperty(timerProperty) as number;
          if (currentTimer !== undefined && currentTimer > 0) {
            player.setDynamicProperty(timerProperty, 0);
          }
        }
      }
    }

  } catch (error) {
    console.warn(`Failed to cleanup item trackers: ${error}`);
  }
}

/**
 * @fileoverview How to Add New Items to the System
 *
 * **For Simple Coil Items:**
 *
 * 1. **Add coil configuration** to the COIL_CONFIGS map:
 *    ```typescript
 *    ["ditsh:your_coil", {
 *      soundId: "item.ditsh.your_coil.use",
 *      soundInterval: 4, // seconds between sound plays
 *      effectId: "speed",
 *      effectAmplifier: 2, // effect level (0 = level 1)
 *      effectDuration: 40 // ticks (2 seconds)
 *    }]
 *    ```
 *
 * 2. **Register the item** in the ITEM_HANDLERS map:
 *    ```typescript
 *    ["ditsh:your_coil", (player: Player) => handleCoilForPlayer(player, "ditsh:your_coil")],
 *    ```
 *
 * **For Advanced Items with Custom Components:**
 *
 * 1. **Create a separate TypeScript file** (e.g., `src/ithiefi/ditsh/items/your_item.ts`)
 * 2. **Implement a custom component class** with onConsume, onUse, etc.
 * 3. **Register the component** in the startup event:
 *    ```typescript
 *    export function registerYourItemComponent(registry: ItemComponentRegistry): void {
 *      registry.registerCustomComponent("ditsh:your_item", new YourItemComponent());
 *    }
 *    ```
 * 4. **Add component to item JSON**:
 *    ```json
 *    "minecraft:custom_components": ["ditsh:your_item"]
 *    ```
 *
 * **Common Steps for All Items:**
 * - Create item definition in `packs/BP/items/ithiefi/ditsh/`
 * - Add sound definitions in `packs/RP/sounds/sound_definitions.json`
 * - Create attachable in `packs/RP/attachables/ithiefi/ditsh/`
 * - Add translation keys in `packs/RP/texts/en_US.lang`
 *
 * The system supports both simple handler-based items and advanced component-based items!
 */