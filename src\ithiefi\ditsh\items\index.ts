/**
 * @fileoverview Centralized Item Handler System
 *
 * This module provides a centralized system for handling all custom items in the DitSH add-on.
 * It manages item detection, routing to specific handlers, and provides utilities for item management.
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 1.0.0
 *
 * @remarks
 * This system follows a registry pattern where each item handler is registered with its item ID.
 * The main handler function checks all players' main hand items and routes to appropriate handlers.
 * This design allows for easy addition of new items without modifying the core system.
 *
 * @example
 * ```typescript
 * // In main.ts
 * import { handleAllItems } from "./items/index";
 *
 * system.runInterval(() => {
 *   handleAllItems();
 * }, 20); // Run every second
 * ```
 */

import {
  world,
  GameMode,
  Player,
  EntityEquippableComponent,
  EntityComponentTypes,
  EquipmentSlot,
  ItemStack,
  ItemCompleteUseAfterEvent,
  EntityInventoryComponent,
  ItemLockMode
} from "@minecraft/server";

// All coil functionality is now centralized in this file
// Individual coil files are no longer needed for the main functionality

/**
 * Type definition for item handler functions
 * Each handler receives the player holding the item
 */
type ItemHandler = (player: Player) => void;

/**
 * Registry mapping item IDs to their corresponding handler functions
 * This allows for easy addition of new items without modifying core logic
 *
 * @example Adding a new item:
 * ```typescript
 * // 1. Create your item handler function in a separate file
 * // 2. Import it at the top of this file
 * // 3. Add it to this registry:
 * ["ditsh:your_new_item", handleYourNewItem]
 * ```
 */
const ITEM_HANDLERS: Map<string, ItemHandler> = new Map([
  ["ditsh:insane_decay_coil", (player: Player) => handleCoilForPlayer(player, "ditsh:insane_decay_coil")],
  ["ditsh:speed_coil", (player: Player) => handleCoilForPlayer(player, "ditsh:speed_coil")],
  ["ditsh:special_coil", (player: Player) => handleCoilForPlayer(player, "ditsh:special_coil")],
  ["ditsh:sandwich", (player: Player) => handleSandwichForPlayer(player)],
]);

/**
 * Configuration for each coil type including sound settings and effects
 */
interface CoilConfig {
  soundId: string;
  soundInterval: number;
  effectId: string;
  effectAmplifier: number;
  effectDuration: number;
}

/**
 * Registry of coil configurations
 */
const COIL_CONFIGS: Map<string, CoilConfig> = new Map([
  ["ditsh:insane_decay_coil", {
    soundId: "item.ditsh.insane_decay_coil.use",
    soundInterval: 5,
    effectId: "speed",
    effectAmplifier: 3, // Swiftness IV
    effectDuration: 40
  }],
  ["ditsh:special_coil", {
    soundId: "item.ditsh.special_coil.use",
    soundInterval: 2,
    effectId: "speed",
    effectAmplifier: 1, // Swiftness II
    effectDuration: 40
  }],
  ["ditsh:speed_coil", {
    soundId: "item.ditsh.speed_coil.use",
    soundInterval: 3,
    effectId: "speed",
    effectAmplifier: 0, // Swiftness I
    effectDuration: 40
  }]
]);

/**
 * Set to track players currently holding any coil item
 * Used for cleanup when players stop holding items
 */
const playersHoldingCoils: Set<string> = new Set();

/**
 * Gets the item currently held in a player's main hand.
 * Uses the EntityEquippableComponent for reliable item detection.
 *
 * @param player - The player to check
 * @returns The ItemStack in the main hand, or undefined if no item or component not available
 *
 * @remarks
 * - Uses explicit type casting for proper TypeScript interface support
 * - Returns undefined if the player doesn't have an equippable component
 * - Returns undefined if no item is in the main hand slot
 *
 * @example
 * ```typescript
 * const mainHandItem = getPlayerMainHandItem(player);
 * if (mainHandItem) {
 *   console.log(`Player is holding: ${mainHandItem.typeId}`);
 * }
 * ```
 */
export function getPlayerMainHandItem(player: Player): ItemStack | undefined {
  try {
    // Get the equippable component from the player
    const equippableComponent: EntityEquippableComponent | undefined = player.getComponent(
      EntityComponentTypes.Equippable
    ) as EntityEquippableComponent;

    if (!equippableComponent) {
      return undefined;
    }

    // Get the item in the main hand
    return equippableComponent.getEquipment(EquipmentSlot.Mainhand);
  } catch (error) {
    console.warn(`Failed to get main hand item for player ${player.name}: ${error}`);
    return undefined;
  }
}

/**
 * Centralized coil sound effect controller.
 * Handles sound timing and playback for all coil types.
 *
 * @param player - The player holding the coil
 * @param itemId - The coil item identifier
 *
 * @description This function:
 * - Gets the coil configuration for the specific item
 * - Manages sound timing using dynamic properties
 * - Plays sound at the configured interval for each coil type
 * - Provides centralized sound management for all coils
 *
 * @remarks
 * - Uses dynamic properties to track sound timing per player per item
 * - Each coil type has its own timer property to avoid conflicts
 * - Sound intervals are configurable per coil type
 * - Automatically resets timer when sound is played
 *
 * @example
 * ```typescript
 * // Called from individual coil handlers
 * handleCoilSoundEffect(player, "ditsh:speed_coil");
 * ```
 */
function handleCoilSoundEffect(player: Player, itemId: string): void {
  try {
    const config = COIL_CONFIGS.get(itemId);
    if (!config) {
      return; // No configuration found for this item
    }

    // Create unique timer property for this coil type
    const timerProperty = `ditsh:${itemId.split(':')[1]}_sound_timer`;

    // Get current timer value
    const soundTimer: number = (player.getDynamicProperty(timerProperty) as number) ?? 0;

    // Check if the configured interval has passed
    if (soundTimer >= config.soundInterval) {
      // Play the sound effect for this specific player only
      player.runCommand(`playsound ${config.soundId} @s ~ ~ ~ 1.0 1.0`);

      // Reset the timer
      player.setDynamicProperty(timerProperty, 0);
    } else {
      // Increment the timer by 1 second (since this runs every second)
      player.setDynamicProperty(timerProperty, soundTimer + 1);
    }

  } catch (error) {
    console.warn(`Failed to handle coil sound effect for player ${player.name}: ${error}`);
  }
}

/**
 * Centralized coil effect controller.
 * Handles effect application for all coil types.
 *
 * @param player - The player holding the coil
 * @param itemId - The coil item identifier
 *
 * @description This function:
 * - Gets the coil configuration for the specific item
 * - Applies the configured effect with proper amplifier and duration
 * - Provides centralized effect management for all coils
 *
 * @remarks
 * - Effect settings are configurable per coil type
 * - Uses showParticles: false to avoid visual clutter
 * - Effect duration ensures no gaps between applications
 *
 * @example
 * ```typescript
 * // Called from individual coil handlers
 * handleCoilEffect(player, "ditsh:special_coil");
 * ```
 */
function handleCoilEffect(player: Player, itemId: string): void {
  try {
    const config = COIL_CONFIGS.get(itemId);
    if (!config) {
      return; // No configuration found for this item
    }

    // Apply the configured effect
    player.addEffect(config.effectId, config.effectDuration, {
      amplifier: config.effectAmplifier,
      showParticles: false // Don't show particles to avoid visual clutter
    });

  } catch (error) {
    console.warn(`Failed to handle coil effect for player ${player.name}: ${error}`);
  }
}

/**
 * Unified coil handler that manages both effects and sounds.
 * This is the main handler function that should be called for all coil types.
 *
 * @param player - The player holding the coil
 * @param itemId - The coil item identifier
 *
 * @description This function:
 * - Tracks that the player is currently holding a coil
 * - Applies the appropriate effect for the coil type
 * - Manages sound timing and playback
 * - Provides unified handling for all coil functionality
 *
 * @remarks
 * - This replaces the individual coil handler functions
 * - Centralizes all coil logic in one place
 * - Maintains the same functionality as individual handlers
 * - Easier to maintain and extend for new coil types
 *
 * @example
 * ```typescript
 * // Used by the centralized item system
 * const handler = ITEM_HANDLERS.get("ditsh:insane_decay_coil");
 * if (handler) {
 *   handler(player);
 * }
 * ```
 */
export function handleCoilForPlayer(player: Player, itemId: string): void {
  try {
    // Track that this player is currently holding a coil
    playersHoldingCoils.add(player.id);

    // Handle effect application
    handleCoilEffect(player, itemId);

    // Handle sound timing and playback
    handleCoilSoundEffect(player, itemId);

  } catch (error) {
    console.warn(`Failed to handle coil for player ${player.name}: ${error}`);
  }
}

/**
 * Handles the sandwich item functionality.
 * The sandwich is a special food item that can be eaten when hunger is full,
 * provides regeneration and swiftness effects, and locks in the player's inventory.
 *
 * @param player - The player holding the sandwich
 *
 * @description This function:
 * - Registers an event listener for when the sandwich is consumed
 * - Applies regeneration and swiftness effects for 30 seconds each
 * - Locks the sandwich item in the player's inventory slot to prevent removal
 *
 * @remarks
 * - The sandwich can be eaten even when hunger bar is full (configured in JSON)
 * - Effects are applied after consumption is complete
 * - Item locking prevents the sandwich from being moved, dropped, or crafted with
 * - Uses ItemLockMode.slot for maximum restriction
 *
 * @example
 * ```typescript
 * // Called from the centralized item system
 * const handler = ITEM_HANDLERS.get("ditsh:sandwich");
 * if (handler) {
 *   handler(player);
 * }
 * ```
 */
export function handleSandwichForPlayer(player: Player): void {
  try {
    // The sandwich functionality is primarily handled through the food component in JSON
    // and the item complete use event listener set up in the main initialization
    // This function serves as a placeholder for any additional real-time logic needed

    // For now, we don't need to do anything here since the sandwich effects
    // are handled by the food component and the locking is handled by the event listener

  } catch (error) {
    console.warn(`Failed to handle sandwich for player ${player.name}: ${error}`);
  }
}

/**
 * Handles the sandwich consumption event.
 * This function is called when a player completes eating the sandwich.
 *
 * @param event - The ItemCompleteUseAfterEvent containing event details
 *
 * @description This function:
 * - Verifies the consumed item is a sandwich
 * - Locks the sandwich item in the player's inventory slot
 * - Prevents the item from being moved, dropped, or crafted with
 *
 * @remarks
 * - Called by the world.afterEvents.itemCompleteUse event listener
 * - Uses ItemLockMode.slot for maximum item restriction
 * - The effects (regeneration and swiftness) are already applied by the food component
 * - Item locking ensures the sandwich remains in the player's inventory permanently
 */
function handleSandwichConsumption(event: ItemCompleteUseAfterEvent): void {
  try {
    // Check if the consumed item is a sandwich
    if (!event.itemStack || event.itemStack.typeId !== "ditsh:sandwich") {
      return;
    }

    // Check if the source is a player
    if (!event.source || event.source.typeId !== "minecraft:player") {
      return;
    }

    const player: Player = event.source as Player;

    // Get the player's inventory component
    const inventoryComponent: EntityInventoryComponent | undefined = player.getComponent(
      EntityComponentTypes.Inventory
    ) as EntityInventoryComponent;

    if (!inventoryComponent) {
      console.warn(`Failed to get inventory component for player ${player.name}`);
      return;
    }

    const container = inventoryComponent.container;
    if (!container) {
      console.warn(`Failed to get container for player ${player.name}`);
      return;
    }

    // Find the sandwich in the player's inventory and lock it
    for (let i = 0; i < container.size; i++) {
      const slot = container.getSlot(i);
      const item = slot.getItem();

      if (item && item.typeId === "ditsh:sandwich") {
        // Lock the item in this slot
        slot.lockMode = ItemLockMode.slot;
        slot.keepOnDeath = true; // Also keep the item on death

        console.log(`Locked sandwich in slot ${i} for player ${player.name}`);
        break; // Only lock the first sandwich found
      }
    }

  } catch (error) {
    console.warn(`Failed to handle sandwich consumption: ${error}`);
  }
}

/**
 * Initializes the sandwich item system by setting up event listeners.
 * This function should be called once during add-on initialization.
 *
 * @description This function:
 * - Sets up the itemCompleteUse event listener for sandwich consumption
 * - Ensures the sandwich locking mechanism is active
 *
 * @remarks
 * - Should be called from the main initialization function
 * - Only needs to be called once per world load
 * - The event listener will handle all sandwich consumption events
 *
 * @example
 * ```typescript
 * // Called from main.ts during initialization
 * initializeSandwichSystem();
 * ```
 */
export function initializeSandwichSystem(): void {
  try {
    // Set up the event listener for sandwich consumption
    world.afterEvents.itemCompleteUse.subscribe(handleSandwichConsumption);

    console.log("Sandwich system initialized successfully");
  } catch (error) {
    console.warn(`Failed to initialize sandwich system: ${error}`);
  }
}

/**
 * Handles all custom items for all players in the world.
 * This is the main entry point for the item system that should be called from main.ts.
 *
 * @description This function:
 * - Gets all players in survival/adventure mode (excludes creative/spectator)
 * - Checks each player's main hand item using getPlayerMainHandItem
 * - Routes to appropriate item handlers based on item ID
 * - Calls the legacy handleInsaneDecayCoil for backward compatibility
 *
 * @remarks
 * - Currently maintains backward compatibility with the existing insane decay coil system
 * - Future items can be added to the ITEM_HANDLERS registry
 * - Individual item handlers manage their own state and effects
 * - Players in creative/spectator mode are excluded to prevent unintended effects
 *
 * @example
 * ```typescript
 * // Called from main.ts interval system
 * system.runInterval(() => {
 *   try {
 *     handleAllItems();
 *   } catch (error) {
 *     console.warn(`Failed to handle items: ${error}`);
 *   }
 * }, 20);
 * ```
 */
export function handleAllItems(): void {
  try {
    // Get all players excluding creative and spectator modes
    const allPlayers: Player[] = world
      .getAllPlayers()
      .filter((player: Player) =>
        player.getGameMode() !== GameMode.Creative &&
        player.getGameMode() !== GameMode.Spectator
      );

    // Track which players are holding items for cleanup purposes
    const playersWithItems: Set<string> = new Set();

    for (const player of allPlayers) {
      try {
        const mainHandItem = getPlayerMainHandItem(player);

        if (mainHandItem) {
          // Check if we have a handler for this item
          const handler = ITEM_HANDLERS.get(mainHandItem.typeId);
          if (handler) {
            // Call the specific item handler
            handler(player);
            playersWithItems.add(player.id);
          }
        }
      } catch (playerError) {
        console.warn(`Failed to process items for player ${player.name}: ${playerError}`);
      }
    }

    // Clean up tracking for players who are no longer holding items
    // This is handled by individual item cleanup functions called from cleanupAllItemTrackers

  } catch (error) {
    console.warn(`Failed to handle all items: ${error}`);
  }
}

/**
 * Cleans up all item-related tracking data.
 * This function should be called periodically to prevent memory leaks.
 *
 * @description This function:
 * - Calls cleanup functions for all registered item handlers
 * - Removes stale tracking data for disconnected players
 * - Maintains optimal performance by cleaning up unused data
 *
 * @remarks
 * - Should be called periodically (e.g., every 10 seconds) from a cleanup interval
 * - Individual item handlers should export their own cleanup functions
 * - Safe to call frequently as it only processes tracking data
 *
 * @example
 * ```typescript
 * // Called from a cleanup interval in entities/index.ts
 * system.runInterval(() => {
 *   cleanupAllItemTrackers();
 * }, 200); // Every 10 seconds
 * ```
 */
export function cleanupAllItemTrackers(): void {
  try {
    // Get all current players
    const allPlayers: Player[] = world.getAllPlayers();

    // Clean up players who are no longer holding any coils
    // This is done by checking against the current holders set
    const currentHolders: Set<string> = new Set(playersHoldingCoils);
    playersHoldingCoils.clear(); // Clear the set, it will be repopulated by active handlers

    // Reset sound timers for players who stopped holding coils
    for (const player of allPlayers) {
      const playerId: string = player.id;

      // If player was not holding any coil this cycle, reset all their coil timers
      if (!currentHolders.has(playerId)) {
        // Reset timers for all coil types
        for (const [itemId] of COIL_CONFIGS) {
          const timerProperty = `ditsh:${itemId.split(':')[1]}_sound_timer`;
          const currentTimer = player.getDynamicProperty(timerProperty) as number;
          if (currentTimer !== undefined && currentTimer > 0) {
            player.setDynamicProperty(timerProperty, 0);
          }
        }
      }
    }

  } catch (error) {
    console.warn(`Failed to cleanup item trackers: ${error}`);
  }
}

/**
 * @fileoverview How to Add New Coil Items to the System
 *
 * To add a new custom coil item to the DitSH add-on:
 *
 * 1. **Add coil configuration** to the COIL_CONFIGS map:
 *    ```typescript
 *    ["ditsh:your_coil", {
 *      soundId: "item.ditsh.your_coil.use",
 *      soundInterval: 4, // seconds between sound plays
 *      effectId: "speed",
 *      effectAmplifier: 2, // effect level (0 = level 1)
 *      effectDuration: 40 // ticks (2 seconds)
 *    }]
 *    ```
 *
 * 2. **Register the item** in the ITEM_HANDLERS map:
 *    ```typescript
 *    ["ditsh:your_coil", (player: Player) => handleCoilForPlayer(player, "ditsh:your_coil")],
 *    ```
 *
 * 3. **Create the item definition** in `packs/BP/items/ithiefi/ditsh/your_coil.i.json`
 *
 * 4. **Add sound definition** in `packs/RP/sounds/sound_definitions.json`:
 *    ```json
 *    "item.ditsh.your_coil.use": {
 *      "category": "player",
 *      "sounds": [{ "name": "sounds/ithiefi/ditsh/items/your_coil/use", "is3D": false }]
 *    }
 *    ```
 *
 * 5. **Create attachable** in `packs/RP/attachables/ithiefi/ditsh/your_coil.at.json`
 *
 * That's it! The centralized system will automatically handle:
 * - Effect application with your configured settings
 * - Sound timing and playback at your specified interval
 * - Dynamic property management for sound timers
 * - Cleanup when players stop holding the item
 *
 * No need to create separate handler files - everything is managed centrally!
 */