import { world, GameMode, EntityComponentTypes, EquipmentSlot } from "@minecraft/server";
import { SandwichComponent } from "./sandwich";
const ITEM_HANDLERS = new Map([
    ["ditsh:insane_decay_coil", (player) => handleCoilForPlayer(player, "ditsh:insane_decay_coil")],
    ["ditsh:speed_coil", (player) => handleCoilForPlayer(player, "ditsh:speed_coil")],
    ["ditsh:special_coil", (player) => handleCoilForPlayer(player, "ditsh:special_coil")],
]);
const COIL_CONFIGS = new Map([
    ["ditsh:insane_decay_coil", {
            soundId: "item.ditsh.insane_decay_coil.use",
            soundInterval: 5,
            effectId: "speed",
            effectAmplifier: 3,
            effectDuration: 40
        }],
    ["ditsh:special_coil", {
            soundId: "item.ditsh.special_coil.use",
            soundInterval: 2,
            effectId: "speed",
            effectAmplifier: 1,
            effectDuration: 40
        }],
    ["ditsh:speed_coil", {
            soundId: "item.ditsh.speed_coil.use",
            soundInterval: 3,
            effectId: "speed",
            effectAmplifier: 0,
            effectDuration: 40
        }]
]);
const playersHoldingCoils = new Set();
export function getPlayerMainHandItem(player) {
    try {
        const equippableComponent = player.getComponent(EntityComponentTypes.Equippable);
        if (!equippableComponent) {
            return undefined;
        }
        return equippableComponent.getEquipment(EquipmentSlot.Mainhand);
    }
    catch (error) {
        console.warn(`Failed to get main hand item for player ${player.name}: ${error}`);
        return undefined;
    }
}
function handleCoilSoundEffect(player, itemId) {
    try {
        const config = COIL_CONFIGS.get(itemId);
        if (!config) {
            return;
        }
        const timerProperty = `ditsh:${itemId.split(':')[1]}_sound_timer`;
        const soundTimer = player.getDynamicProperty(timerProperty) ?? 0;
        if (soundTimer >= config.soundInterval) {
            player.runCommand(`playsound ${config.soundId} @s ~ ~ ~ 1.0 1.0`);
            player.setDynamicProperty(timerProperty, 0);
        }
        else {
            player.setDynamicProperty(timerProperty, soundTimer + 1);
        }
    }
    catch (error) {
        console.warn(`Failed to handle coil sound effect for player ${player.name}: ${error}`);
    }
}
function handleCoilEffect(player, itemId) {
    try {
        const config = COIL_CONFIGS.get(itemId);
        if (!config) {
            return;
        }
        player.addEffect(config.effectId, config.effectDuration, {
            amplifier: config.effectAmplifier,
            showParticles: false
        });
    }
    catch (error) {
        console.warn(`Failed to handle coil effect for player ${player.name}: ${error}`);
    }
}
export function handleCoilForPlayer(player, itemId) {
    try {
        playersHoldingCoils.add(player.id);
        handleCoilEffect(player, itemId);
        handleCoilSoundEffect(player, itemId);
    }
    catch (error) {
        console.warn(`Failed to handle coil for player ${player.name}: ${error}`);
    }
}
export function registerSandwichComponent(itemComponentRegistry) {
    try {
        itemComponentRegistry.registerCustomComponent("ditsh:sandwich", new SandwichComponent());
        console.log("Sandwich custom component registered successfully");
    }
    catch (error) {
        console.warn(`Failed to register sandwich component: ${error}`);
    }
}
export function handleAllItems() {
    try {
        const allPlayers = world
            .getAllPlayers()
            .filter((player) => player.getGameMode() !== GameMode.Creative &&
            player.getGameMode() !== GameMode.Spectator);
        const playersWithItems = new Set();
        for (const player of allPlayers) {
            try {
                const mainHandItem = getPlayerMainHandItem(player);
                if (mainHandItem) {
                    const handler = ITEM_HANDLERS.get(mainHandItem.typeId);
                    if (handler) {
                        handler(player);
                        playersWithItems.add(player.id);
                    }
                }
            }
            catch (playerError) {
                console.warn(`Failed to process items for player ${player.name}: ${playerError}`);
            }
        }
    }
    catch (error) {
        console.warn(`Failed to handle all items: ${error}`);
    }
}
export function cleanupAllItemTrackers() {
    try {
        const allPlayers = world.getAllPlayers();
        const currentHolders = new Set(playersHoldingCoils);
        playersHoldingCoils.clear();
        for (const player of allPlayers) {
            const playerId = player.id;
            if (!currentHolders.has(playerId)) {
                for (const [itemId] of COIL_CONFIGS) {
                    const timerProperty = `ditsh:${itemId.split(':')[1]}_sound_timer`;
                    const currentTimer = player.getDynamicProperty(timerProperty);
                    if (currentTimer !== undefined && currentTimer > 0) {
                        player.setDynamicProperty(timerProperty, 0);
                    }
                }
            }
        }
    }
    catch (error) {
        console.warn(`Failed to cleanup item trackers: ${error}`);
    }
}
