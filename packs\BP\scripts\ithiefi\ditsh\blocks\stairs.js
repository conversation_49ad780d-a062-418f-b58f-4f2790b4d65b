import { world, system } from "@minecraft/server";
world.afterEvents.playerPlaceBlock.subscribe((eventData) => {
    const block = eventData.block;
    if (block.hasTag("ditsh:stair")) {
        updateNeighborStairs(block);
    }
});
world.beforeEvents.playerBreakBlock.subscribe((eventData) => {
    const block = eventData.block;
    system.run(() => {
        updateDestroyedStair(block);
    });
});
function updateDestroyedStair(block) {
    updateStair(block);
    const neighbors = [block.north(), block.south(), block.east(), block.west()];
    for (const neighbor of neighbors) {
        if (neighbor && neighbor.hasTag("ditsh:stair")) {
            updateStair(neighbor);
        }
    }
}
function updateNeighborStairs(block) {
    updateStair(block);
    const blockHalf = block.permutation.getState("minecraft:vertical_half");
    const neighbors = [block.north(), block.south(), block.east(), block.west()];
    for (const neighbor of neighbors) {
        if (neighbor &&
            neighbor.permutation.getState("minecraft:vertical_half") === blockHalf &&
            neighbor.hasTag("ditsh:stair")) {
            updateStair(neighbor);
        }
    }
}
function updateStair(block) {
    const northBlock = block.north();
    const southBlock = block.south();
    const eastBlock = block.east();
    const westBlock = block.west();
    const blockChange = block.permutation;
    const stairHalf = block.permutation.getState("minecraft:vertical_half");
    const stairDirection = block.permutation.getState("minecraft:cardinal_direction");
    const northHalf = northBlock?.permutation.getState("minecraft:vertical_half");
    const southHalf = southBlock?.permutation.getState("minecraft:vertical_half");
    const eastHalf = eastBlock?.permutation.getState("minecraft:vertical_half");
    const westHalf = westBlock?.permutation.getState("minecraft:vertical_half");
    const northDirection = northBlock?.permutation.getState("minecraft:cardinal_direction");
    const southDirection = southBlock?.permutation.getState("minecraft:cardinal_direction");
    const eastDirection = eastBlock?.permutation.getState("minecraft:cardinal_direction");
    const westDirection = westBlock?.permutation.getState("minecraft:cardinal_direction");
    const northIsStair = northBlock?.hasTag("ditsh:stair") ?? false;
    const southIsStair = southBlock?.hasTag("ditsh:stair") ?? false;
    const eastIsStair = eastBlock?.hasTag("ditsh:stair") ?? false;
    const westIsStair = westBlock?.hasTag("ditsh:stair") ?? false;
    let stairType = 1;
    if (stairDirection === "north") {
        if (northIsStair && northHalf === stairHalf && northDirection === "west") {
            stairType = 4;
        }
        else if (northIsStair && northHalf === stairHalf && northDirection === "east") {
            stairType = 5;
        }
        else if (southIsStair && southHalf === stairHalf && southDirection === "west") {
            stairType = 2;
        }
        else if (southIsStair && southHalf === stairHalf && southDirection === "east") {
            stairType = 3;
        }
    }
    else if (stairDirection === "south") {
        if (northIsStair && northHalf === stairHalf && northDirection === "west") {
            stairType = 3;
        }
        else if (northIsStair && northHalf === stairHalf && northDirection === "east") {
            stairType = 2;
        }
        else if (southIsStair && southHalf === stairHalf && southDirection === "west") {
            stairType = 4;
        }
        else if (southIsStair && southHalf === stairHalf && southDirection === "east") {
            stairType = 5;
        }
    }
    else if (stairDirection === "west") {
        if (westIsStair && westHalf === stairHalf && westDirection === "north") {
            stairType = 5;
        }
        else if (westIsStair && westHalf === stairHalf && westDirection === "south") {
            stairType = 4;
        }
        else if (eastIsStair && eastHalf === stairHalf && eastDirection === "north") {
            stairType = 3;
        }
        else if (eastIsStair && eastHalf === stairHalf && eastDirection === "south") {
            stairType = 2;
        }
    }
    else if (stairDirection === "east") {
        if (westIsStair && westHalf === stairHalf && westDirection === "north") {
            stairType = 2;
        }
        else if (westIsStair && westHalf === stairHalf && westDirection === "south") {
            stairType = 3;
        }
        else if (eastIsStair && eastHalf === stairHalf && eastDirection === "north") {
            stairType = 5;
        }
        else if (eastIsStair && eastHalf === stairHalf && eastDirection === "south") {
            stairType = 4;
        }
    }
    block.setPermutation(blockChange.withState("ditsh:type", stairType));
}
