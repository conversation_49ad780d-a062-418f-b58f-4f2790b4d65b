import { world, GameMode, EntityComponentTypes, EquipmentSlot, system } from "@minecraft/server";
const INSANE_DECAY_COIL_ID = "ditsh:insane_decay_coil";
const SWIFTNESS_EFFECT_ID = "speed";
const SWIFTNESS_AMPLIFIER = 3;
const EFFECT_DURATION = 40;
const SOUND_EFFECT_ID = "item.ditsh.insane_decay_coil.use";
const SOUND_INTERVAL = 100;
const playerSoundTracker = new Map();
export function handleInsaneDecayCoil() {
    try {
        const allPlayers = world
            .getAllPlayers()
            .filter((player) => player.getGameMode() !== GameMode.Creative &&
            player.getGameMode() !== GameMode.Spectator);
        for (const player of allPlayers) {
            try {
                const equippableComponent = player.getComponent(EntityComponentTypes.Equippable);
                if (!equippableComponent) {
                    continue;
                }
                const mainHandItem = equippableComponent.getEquipment(EquipmentSlot.Mainhand);
                if (mainHandItem && mainHandItem.typeId === INSANE_DECAY_COIL_ID) {
                    player.addEffect(SWIFTNESS_EFFECT_ID, EFFECT_DURATION, {
                        amplifier: SWIFTNESS_AMPLIFIER,
                        showParticles: false
                    });
                    const playerId = player.id;
                    const currentTick = system.currentTick;
                    const lastSoundTick = playerSoundTracker.get(playerId) ?? 0;
                    if (currentTick - lastSoundTick >= SOUND_INTERVAL) {
                        player.runCommand(`playsound ${SOUND_EFFECT_ID} @s ~ ~ ~ 1.0 1.0`);
                        playerSoundTracker.set(playerId, currentTick);
                    }
                }
                else {
                    const playerId = player.id;
                    if (playerSoundTracker.has(playerId)) {
                        playerSoundTracker.delete(playerId);
                    }
                }
            }
            catch (playerError) {
                console.warn(`Failed to process insane decay coil for player ${player.name}: ${playerError}`);
            }
        }
    }
    catch (error) {
        console.warn(`Failed to handle insane decay coil system: ${error}`);
    }
}
export function cleanupInsaneDecayCoilSoundTracker() {
    try {
        const currentPlayerIds = new Set(world.getAllPlayers().map((player) => player.id));
        for (const [playerId] of playerSoundTracker) {
            if (!currentPlayerIds.has(playerId)) {
                playerSoundTracker.delete(playerId);
            }
        }
    }
    catch (error) {
        console.warn(`Failed to cleanup insane decay coil sound tracker: ${error}`);
    }
}
